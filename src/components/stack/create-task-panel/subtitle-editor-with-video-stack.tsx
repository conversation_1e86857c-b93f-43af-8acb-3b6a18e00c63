"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { StackRef } from "@/components/ui/stack";
import { ArrowLeft, Edit3, Plus, Trash2, Wand2, Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { RefObject, useState, useEffect } from "react";
import Image from "next/image";

import { useAtomValue, useSetAtom } from "jotai";
import {
  subtitleDataAtom,
  addSubtitleAtom,
  updateBothSubtitlesAtom,
  deleteSubtitleAtom,
  SubtitleItem
} from "@/stores/slices/subtitle_store";
import { currentTaskAtom } from "@/stores/slices/current_task";
import { ArtPlayer } from "@/components/common/art-player";

import { isYoutubeUrl, getYoutubeVideoId } from "@/utils/video-format";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { toast } from "sonner";



interface SubtitleEditorWithVideoStackProps {
  stackRef: RefObject<StackRef>;
}

interface SubtitleRowProps {
  subtitle: SubtitleItem;
  editingField: string | null;
  onFieldEdit: (field: string | null) => void;
  onSave: (updates: Partial<SubtitleItem>) => void;
  onDelete: () => void;
  isActive: boolean;
  onSeekTo: (time: number) => void;
}

// Convert time string to seconds
const timeStringToSeconds = (timeString: string): number => {
  const [time, milliseconds] = timeString.split(',');
  const [hours, minutes, seconds] = time.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds + (parseInt(milliseconds || '0') / 1000);
};

// Convert seconds to time string
const secondsToTimeString = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
};

const SubtitleRow = ({ 
  subtitle, 
  editingField, 
  onFieldEdit, 
  onSave, 
  onDelete,
  isActive,
  onSeekTo
}: SubtitleRowProps) => {
  const [editData, setEditData] = useState({
    startTime: subtitle.startTime,
    endTime: subtitle.endTime,
    text: subtitle.text,
    translatedText: subtitle.translatedText || "",
  });

  // Update edit data when subtitle changes
  useEffect(() => {
    setEditData({
      startTime: subtitle.startTime,
      endTime: subtitle.endTime,
      text: subtitle.text,
      translatedText: subtitle.translatedText || "",
    });
  }, [subtitle]);

  const handleSave = (field: string) => {
    const updates: Partial<SubtitleItem> = {};
    
    switch (field) {
      case 'startTime':
        updates.startTime = editData.startTime;
        break;
      case 'endTime':
        updates.endTime = editData.endTime;
        break;
      case 'text':
        updates.text = editData.text;
        break;
      case 'translatedText':
        updates.translatedText = editData.translatedText;
        break;
    }
    
    onSave(updates);
    onFieldEdit(null);
  };

  const handleCancel = () => {
    setEditData({
      startTime: subtitle.startTime,
      endTime: subtitle.endTime,
      text: subtitle.text,
      translatedText: subtitle.translatedText || "",
    });
    onFieldEdit(null);
  };

  const handleKeyDown = (e: React.KeyboardEvent, field: string) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave(field);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleSeekToStart = () => {
    const startSeconds = timeStringToSeconds(subtitle.startTime);
    onSeekTo(startSeconds);
  };

  return (
    <div className={cn(
      "grid grid-cols-6 gap-2 p-3 border-b hover:bg-muted/30 transition-colors cursor-pointer",
      editingField && "bg-primary/5 border-primary/20",
      isActive && "bg-accent/50 border-accent"
    )} onClick={handleSeekToStart}>
      {/* Start Time */}
      <div className="flex flex-col gap-1">
        {editingField === 'startTime' ? (
          <Input
            value={editData.startTime}
            onChange={(e) => setEditData(prev => ({ ...prev, startTime: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'startTime')}
            onBlur={() => handleSave('startTime')}
            className="h-8 text-xs"
            placeholder="00:00:00,000"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-xs font-mono bg-muted/50 px-2 py-1 rounded cursor-pointer hover:bg-muted transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('startTime');
            }}
          >
            {subtitle.startTime}
          </div>
        )}
      </div>

      {/* End Time */}
      <div className="flex flex-col gap-1">
        {editingField === 'endTime' ? (
          <Input
            value={editData.endTime}
            onChange={(e) => setEditData(prev => ({ ...prev, endTime: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'endTime')}
            onBlur={() => handleSave('endTime')}
            className="h-8 text-xs"
            placeholder="00:00:03,000"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-xs font-mono bg-muted/50 px-2 py-1 rounded cursor-pointer hover:bg-muted transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('endTime');
            }}
          >
            {subtitle.endTime}
          </div>
        )}
      </div>

      {/* Original Text */}
      <div className="flex flex-col gap-1">
        {editingField === 'text' ? (
          <Textarea
            value={editData.text}
            onChange={(e) => setEditData(prev => ({ ...prev, text: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'text')}
            onBlur={() => handleSave('text')}
            className="min-h-[60px] text-sm resize-none"
            placeholder="Enter subtitle text..."
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-sm leading-relaxed p-2 min-h-[60px] bg-background border rounded cursor-pointer hover:bg-muted/20 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('text');
            }}
          >
            {subtitle.text || (
              <span className="text-muted-foreground italic">Click to add text...</span>
            )}
          </div>
        )}
      </div>

      {/* Translated Text */}
      <div className="flex flex-col gap-1">
        {editingField === 'translatedText' ? (
          <Textarea
            value={editData.translatedText}
            onChange={(e) => setEditData(prev => ({ ...prev, translatedText: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'translatedText')}
            onBlur={() => handleSave('translatedText')}
            className="min-h-[60px] text-sm resize-none"
            placeholder="Enter translation..."
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-sm leading-relaxed p-2 min-h-[60px] bg-background border rounded cursor-pointer hover:bg-muted/20 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('translatedText');
            }}
          >
            {subtitle.translatedText || (
              <span className="text-muted-foreground italic">Click to add translation...</span>
            )}
          </div>
        )}
      </div>

      {/* Duration */}
      <div className="flex flex-col gap-1 justify-center">
        <div className="text-xs text-muted-foreground text-center">
          {((timeStringToSeconds(subtitle.endTime) - timeStringToSeconds(subtitle.startTime))).toFixed(1)}s
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-start gap-1">
        <div className="flex flex-col gap-1">
          <Button 
            size="sm" 
            variant="ghost" 
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }} 
            className="h-7"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          {editingField && (
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={(e) => {
                e.stopPropagation();
                handleCancel();
              }} 
              className="h-7 text-xs"
            >
              ESC
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export const SubtitleEditorWithVideoStack = ({ stackRef }: SubtitleEditorWithVideoStackProps) => {
  const t = useTranslations();
  const subtitleData = useAtomValue(subtitleDataAtom);
  const currentTask = useAtomValue(currentTaskAtom);
  const addSubtitle = useSetAtom(addSubtitleAtom);
  const updateBothSubtitles = useSetAtom(updateBothSubtitlesAtom);
  const deleteSubtitle = useSetAtom(deleteSubtitleAtom);

  // State for tracking which field is being edited
  const [editingField, setEditingField] = useState<{id: string, field: string} | null>(null);
  const [currentTime, setCurrentTime] = useState(0);

  // AI prompt state
  const [showPromptInput, setShowPromptInput] = useState(false);
  const [promptText, setPromptText] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);

  // Subtitle overlay state
  const [currentSubtitle, setCurrentSubtitle] = useState<SubtitleItem | null>(null);

  // For now, we'll use manual subtitle sync
  // The video player will work without automatic subtitle overlay
  // Users can still edit subtitles and they will be saved properly

  // Handle adding subtitle at current time
  const handleAddSubtitle = () => {
    const startTime = secondsToTimeString(0); // Start at 0 for now
    const endTime = secondsToTimeString(3); // Default 3 second duration

    addSubtitle({
      startTime,
      endTime,
      text: "",
      translatedText: ""
    });
  };

  // Handle seeking to specific time - simplified for now
  const handleSeekTo = (time: number) => {
    // For now, just update the current time state
    // In the future, this could control the video player
    setCurrentTime(time);
  };

  const handleBack = () => {
    stackRef.current?.pop();
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center gap-3 border-b bg-card/50 p-3">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBack}
          className="h-7 w-7 p-0"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex items-center gap-2 flex-1">
          <Edit3 className="h-4 w-4 text-primary" />
          <h2 className="text-sm font-medium">
            {t("form.fields.subtitleEditor.title")} with Video Preview
          </h2>
        </div>
      </div>

      {/* Main Content - Split Layout */}
      <div className="flex-1 flex">
        {/* Video Preview Panel */}
        <div className="w-1/2 border-r relative">
          {/* Video Player */}
          <div className="p-4">
            <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
              {(() => {
                // Get video URL - prefer downloaded version if available
                const videoUrl = currentTask.DownloadTask?.videoUrl && currentTask.DownloadTask?.downloadStatus === 'success'
                  ? currentTask.DownloadTask.videoUrl
                  : currentTask.videoUrl;

                // Handle case where we have thumbnail but no video URL (thumbnail preview)
                if (!videoUrl && currentTask.thumbnail) {
                  const proxyImageUrl = currentTask.thumbnail.startsWith('data:')
                    ? currentTask.thumbnail
                    : `/api/302/vt/image/proxy?url=${encodeURIComponent(currentTask.thumbnail)}`;

                  return (
                    <div className="relative w-full h-full">
                      <Image
                        src={proxyImageUrl}
                        alt={currentTask.name || "Video thumbnail"}
                        fill
                        className="object-contain"
                        sizes="(max-width: 768px) 100vw, 50vw"
                        priority
                      />
                      <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                        <div className="text-center text-white">
                          <div className="text-sm font-medium">Video Preview</div>
                          <div className="text-xs opacity-75">Thumbnail only</div>
                        </div>
                      </div>
                    </div>
                  );
                }

                // Handle video playback
                if (videoUrl) {
                  const isYoutube = isYoutubeUrl(videoUrl);
                  const youtubeVideoId = isYoutube ? getYoutubeVideoId(videoUrl) : null;

                  // Prepare poster URL
                  const posterUrl = currentTask.thumbnail
                    ? (currentTask.thumbnail.startsWith('data:')
                        ? currentTask.thumbnail
                        : `/api/302/vt/image/proxy?url=${encodeURIComponent(currentTask.thumbnail)}`)
                    : undefined;

                  if (isYoutube && youtubeVideoId) {
                    return (
                      <iframe
                        src={`https://www.youtube.com/embed/${youtubeVideoId}`}
                        className="absolute inset-0 size-full"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                      />
                    );
                  } else {
                    // Use proxy for external URLs, direct URL for local/uploaded videos
                    const finalVideoUrl = videoUrl.startsWith('http') && !videoUrl.includes(window.location.hostname)
                      ? `/api/302/vt/video/proxy?url=${encodeURIComponent(videoUrl)}`
                      : videoUrl;

                    return (
                      <ArtPlayer
                        url={finalVideoUrl}
                        poster={posterUrl}
                        className="absolute inset-0 size-full"
                      />
                    );
                  }
                }

                // Default fallback - no video or thumbnail
                return (
                  <div className="w-full h-full flex items-center justify-center text-white">
                    <div className="text-center space-y-2">
                      <div className="text-sm font-medium">No video loaded</div>
                      <div className="text-xs text-muted-foreground">Please select a video first</div>
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        </div>

        {/* Subtitle Editor Panel */}
        <div className="w-1/2 flex flex-col h-screen overflow-y-auto ml-auto">
          {/* Action Bar - Sticky */}
          <div className="sticky top-0 z-10 bg-background border-b shadow-sm">
            <div className="flex items-center gap-2 bg-muted/30 p-3">
              <Button
                size="sm"
                variant="default"
                onClick={() => setShowPromptInput(!showPromptInput)}
                disabled={isGenerating}
              >
                <Wand2 className="h-3 w-3 mr-1" />
                Create with AI
              </Button>
              <div className="ml-auto text-xs text-muted-foreground">
                {subtitleData.originalSubtitles.length} subtitles
              </div>
            </div>
          </div>

          {/* AI Prompt Input */}
          {showPromptInput && (
            <div className="border-b bg-muted/20 p-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Wand2 className="h-4 w-4 text-primary" />
                  <h3 className="font-medium">Create Subtitles with AI</h3>
                </div>
                <Textarea
                  value={promptText}
                  onChange={(e) => setPromptText(e.target.value)}
                  placeholder="Describe what you want to subtitle (e.g., 'Create subtitles for this cooking video in English and translate to Spanish')"
                  className="min-h-[80px]"
                />
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    onClick={() => {
                      // AI generation logic would go here
                      toast.info('AI subtitle generation coming soon!');
                    }}
                    disabled={!promptText.trim() || isGenerating}
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Wand2 className="h-3 w-3 mr-1" />
                        Generate
                      </>
                    )}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowPromptInput(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Content */}
          {subtitleData.originalSubtitles.length > 0 ? (
            <div className="flex-1 overflow-y-auto">
              {/* Header Row */}
              <div className="sticky top-0 z-10 bg-background border-b">
                <div className="grid grid-cols-6 gap-2 p-3 text-xs font-medium text-muted-foreground bg-muted/30">
                  <div>Start Time</div>
                  <div>End Time</div>
                  <div>Original Text</div>
                  <div>Translation</div>
                  <div>Duration</div>
                  <div>Actions</div>
                </div>
              </div>

              {/* Subtitle Rows */}
              <div className="divide-y">
                {subtitleData.originalSubtitles.map((subtitle) => (
                  <SubtitleRow
                    key={subtitle.id}
                    subtitle={subtitle}
                    editingField={editingField?.id === subtitle.id ? editingField.field : null}
                    onFieldEdit={(field) =>
                      setEditingField(field ? { id: subtitle.id, field } : null)
                    }
                    onSave={(updates) => {
                      updateBothSubtitles({ id: subtitle.id, updates });
                    }}
                    onDelete={() => deleteSubtitle(subtitle.id)}
                    isActive={currentSubtitle?.id === subtitle.id}
                    onSeekTo={handleSeekTo}
                  />
                ))}
              </div>

              {/* Add Subtitle Button */}
              <div className="p-4 border-t bg-muted/10">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleAddSubtitle}
                  className="w-full"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add Subtitle at {currentTime.toFixed(1)}s
                </Button>
              </div>
            </div>
          ) : (
            <div className="min-h-[400px] flex items-center justify-center">
              <div className="text-center space-y-3">
                <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                  <Edit3 className="h-6 w-6 text-muted-foreground" />
                </div>
                <div>
                  <h3 className="font-medium">{t("form.fields.subtitleEditor.noSubtitles")}</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Click "Add Subtitle" to create your first subtitle.
                  </p>
                </div>
                <Button
                  size="sm"
                  onClick={handleAddSubtitle}
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add First Subtitle
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
