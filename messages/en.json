{"auth": {"description": "The creator has enabled verification, please fill in the sharing code below", "form": {"confirm_button": "Confirm", "confirm_button_loading": "Verifying", "input_code": "Please enter the sharing code", "remember_code": "Remember sharing code"}, "title": "Sharing code required"}, "common": {"disabled": "Disabled", "enabled": "Enabled", "hide": "<PERSON>de", "show": "Show"}, "form": {"errors": {"invalidVideoUrl": "Please enter a valid video URL", "required": "This field is required"}, "fields": {"audio_codec": "Audio Codec", "ext": "Format", "filesize": "File Size", "format_id": "Format ID", "formats": "Video Formats", "formatsAvailable": "available formats", "fps": "FPS", "language": {"auto": "Auto", "en": "English", "ja": "Japanese", "zh": "Chinese"}, "resolution": "Resolution", "settings": "Settings", "sourceLanguage": {"auto": "Automatic detection", "label": "Source language", "placeholder": "Select source language"}, "subtitleLayout": {"double": "Bilingual subtitles", "label": "Subtitle layout", "placeholder": "Select subtitle layout", "single": "Monolingual subtitles (translation only)"}, "subtitleStyle": {"background": "Background settings", "backgroundColor": "Background color", "backgroundOpacity": "Background opacity", "backgroundPadding": "Background padding", "basic": "Basic settings", "bold": "Bold", "bottomMargin": "Bottom margin", "font": "Font settings", "fontFamily": "Select font", "fontSize": "Font size", "label": "Subtitle style", "marginV": "vertical margin", "preview": "Subtitle Preview", "primary": "Original text style", "secondary": "Translation text style", "secondaryFontFamily": "Secondary Font", "secondaryFontSize": "Secondary Font Size", "shadow": "Shadow settings", "shadowBlur": "Shadow blur", "shadowColor": "Shadow color", "shadowOffsetX": "Horizontal offset", "shadowOffsetY": "Vertical offset", "shadowOpacity": "Shadow opacity", "showBackground": "Show Background", "showShadow": "Show Shadow", "showStroke": "Show Stroke", "strokeColor": "Stroke color", "strokeWidth": "Stroke width", "textColor": "Text color"}, "subtitleEditor": {"title": "Subtitle Editor", "originalSubtitles": "Original Subtitles", "translatedSubtitles": "Translated Subtitles", "addSubtitle": "Add Subtitle", "deleteSubtitle": "Delete Subtitle", "saveChanges": "Save Changes", "exportSubtitles": "Export Subtitles", "timeStart": "Start Time", "timeEnd": "End Time", "subtitleText": "Subtitle Text", "noSubtitles": "No subtitles available. Generate some first.", "editingMode": "Editing Mode", "previewMode": "Preview Mode"}, "targetLanguage": {"label": "Target language", "placeholder": "Select target language"}, "translation": {"label": "Translation settings"}, "videoUrl": {"label": "Video link (Supports YouTube, Bilibili)", "placeholder": "Please enter video URL"}, "video_codec": "Video Codec", "voiceSeparation": {"label": "Voice separation enhancement"}}, "get_video_info": "Get video information"}, "global": {"cancel": "Cancel", "confirm": "Confirm", "error": {"code_invalid": "Invalid sharing code. For more information, visit 302.AI", "copy_failed": "Co<PERSON> failed", "network_error": "Network service error, please check the network status or try again later!", "not_found": "Page does not exist", "tool_deleted": "This tool has been deleted, please visit <site>302.AI</site> for more information", "tool_disabled": "This tool has been disabled, please visit <site>302.AI</site> for more information", "unknown_error": "An unknown error occurred, please try again later", "video_play_error": "Video playback error"}, "footer": {"copyright_content": "Powered by <logo></logo>", "copyright_leading": "Content is generated by AI and is for reference only"}, "header": {"language_switcher": {"switch_language": "Switch language"}, "theme_switcher": {"dark": "Dark mode", "light": "Light mode", "system": "Follow system", "toggle_theme": "Switch theme"}, "tool_info": {"description": "Current tool related information", "title": "About", "trigger": {"label": "Open/Close Tool Information Popup"}}}, "video": {"approximateSize": "About {size}MB", "confirmSelection": "Submit Task", "duration": "{minutes}m {seconds}s", "exactSize": "{size}MB", "formatSelected": "Video format selected", "gettingInfo": "Getting video information...", "no_video_selected": "No video selected", "onlineVideo": "Online Video", "preview": "Video Preview", "previewAudioNotice": "Note: Preview playback may be silent, but this won't affect the final result. Please ensure the original video link contains audio.", "screenshot": "Screenshot", "settingsUpdated": "Settings updated", "taskCreated": "Task Created", "taskRemoved": "Task Removed", "unknownSize": "Unknown <PERSON><PERSON>", "uploadedVideo": "Uploaded Video"}}, "home": {"header": {"title": "AI Deep Video Translation"}}, "panel": {"taskHistory": {"empty": "No tasks", "title": "Task History"}}, "task": {"ago": " ago", "audio_file": "Audio File", "back": "Back", "cancel": "Cancel Task", "cancel_success": "Task has been canceled, but the current step will complete", "cancel_warning": "Canceling a task will only stop updating its status. The currently executing step will continue and incur corresponding charges. Are you sure you want to cancel?", "clear_filters": "Clear Filters", "complete": "Task Complete", "complete_desc": "Task {taskId} completed", "confidence": "{score}%", "created_at": "Created At", "current_step": "Current Step", "date_from": "Start Date", "date_to": "End Date", "delete": "Delete Task", "delete_success": "Task record has been permanently deleted, but the current step will complete", "delete_warning": "This deletion will permanently remove the local task record and cannot be undone. Note: The currently executing step will continue and incur corresponding charges. Are you sure you want to delete?", "download": {"downloading": "Downloading", "failed": "Download Failed", "preparing": "Preparing download...", "progress_info": "{percentage}% - {speed}/s - {timeLeft} remaining", "starting": "Starting Download", "success": "Download Complete", "time_hours": "{hours} hours", "time_minutes": "{minutes} minutes", "time_seconds": "{seconds} seconds", "unknown_error": "Unknown Error"}, "download_ass": "Download ASS", "download_srt": "Download SRT", "download_subtitles": "Download Subtitles", "download_video": "Download Video", "download_vtt": "Download VTT", "duration": "Duration", "error": "Error Message", "failed": "Operation Failed", "failed_desc": "Task {taskId} failed: {error}", "filter": "Filter", "filter_date": "Filter by Date", "filter_language": "Filter by Language", "filter_status": "Filter by Status", "languages": "Languages", "loading": "Loading...", "more_subtitles": "{count} more segments", "more_words": "{count} more words", "name": "Task Name", "no_search_results": "No matching tasks found", "no_tasks": "No tasks", "open_menu": "Open Menu", "output_video": "Output Video", "pause": "Pause Task", "progress": "Progress", "resume": "Resume Task", "search_placeholder": "Search by name, language or status", "segments": "Segments", "select_date": "Select Date", "server_task_id": "Server Task ID", "settings": "Settings", "sort": "Sort", "source_segments": "Source Subtitles", "source_subtitles": "Source Subtitles", "status": {"completed": "Completed", "fail": "Failed", "failed": "Failed", "pending": "Pending", "processing": "Processing", "queue": "Queued", "status": "Status", "success": "Success"}, "statusLabel": "Status", "step": {"burn": "Burn Subtitles", "details": "Task Step Details", "download": "Download Video", "transcribe": "Extract Subtitles", "translate": "Translate Subtitles", "video_info": "Get Video Info"}, "step_complete": "Step Complete", "step_complete_desc": "Task {taskId} {step} operation completed", "step_failed": "Step Failed", "step_failed_desc": "Task {taskId} {step} operation failed: {error}", "subtitle_preview": "Subtitle Preview", "subtitles": "Subtitles Count", "task_id": "Task ID", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "time_range": "{start} → {end}", "translated_segments": "Translated Subtitles", "translated_subtitles": "Translated Subtitles", "unnamed_task": "Unnamed Task", "updated_at": "Updated At", "video_file": "Video File", "video_info": "Video Info", "video_preview": "Video Preview", "video_thumbnail": "Video Thumbnail", "view_details": "View Details", "view_video": "View Video", "word_segments": "Word-level Subtitles", "word_segments_preview": "Word Segments Preview", "words": "Words"}, "uploader": {"aria": {"dropzone": "Drop zone", "region": "Video upload area"}, "button": {"browse": "Browse video files", "cancel": "Cancel", "cancel_upload": "Cancel Upload", "confirm_upload": "Confirm Upload", "retry": "Choose another file", "upload": "Start upload", "upload_more": "Upload More Files", "uploading": "Uploading..."}, "create_online_task": "Create online video task", "error": {"duration_too_long": "Video duration is too long", "duration_too_short": "Video duration is too short", "file_invalid": "Invalid file", "invalid_dimensions": "Video dimensions are not supported", "invalid_type": "Please upload a valid video file (MP4, MOV, AVI, WEBM)", "max_files_exceeded": "Maximum files exceeded", "max_files_exceeded_description": "You can only upload up to {maxFiles} files", "size_exceeded": "File size cannot exceed 1GB", "upload_failed": "Upload failed, please try again", "validation_failed": "Video validation failed"}, "info": {"drag_drop": "Click or drag video here", "formats": "Supported formats: MP4, WEBM", "max_files": "Maximum files: {maxFiles}", "or": "or", "size_limit": "Video size limit: {maxSize}", "tooltip": {"formats": "Supports uploading MP4, AVI, MOV, WEBM video files", "size": "Maximum file size: {maxSize}"}, "total_progress": "Total Progress", "upload_cancelled": "Upload cancelled", "upload_cancelled_description": "Video upload has been cancelled"}, "preview": {"file_info": "File information", "file_number": "File {number}", "loading": "Loading video...", "pause": "Pause video", "play": "Play video", "ready_to_upload": "Ready to upload", "remove_video": "Remove video", "video_preview": "Video preview"}, "success": {"description": "All videos have been uploaded successfully", "title": "Upload successful", "total_files": "Total Files", "total_size": "Total Size", "view_files": "View Files"}}}